using System;
using System.Collections.Generic;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace FAST_ERP_Backend.Models.Common.Logging
{
    /// <summary>
    /// 簡化的日誌記錄模型
    /// 提供清晰、可讀的日誌結構，避免複雜的序列化格式
    /// </summary>
    public class SimpleLogEntry
    {
        #region Core Properties

        /// <summary> 日誌唯一識別碼 </summary>
        [BsonId]
        [BsonRepresentation(BsonType.String)]
        public Guid Id { get; set; } = Guid.NewGuid();

        /// <summary> 記錄時間戳（UTC+8 台灣時區） </summary>
        [BsonElement("timestamp")]
        [BsonDateTimeOptions(Kind = DateTimeKind.Local)]
        public DateTime Timestamp { get; set; } = DateTime.UtcNow.AddHours(8);

        /// <summary> 日誌級別 </summary>
        [BsonElement("level")]
        public string Level { get; set; } = "Information";

        /// <summary> 日誌訊息 </summary>
        [BsonElement("message")]
        public string Message { get; set; } = string.Empty;

        /// <summary> 來源系統或模組 </summary>
        [BsonElement("source")]
        public string Source { get; set; } = "System";

        /// <summary> 交易識別碼（用於關聯相關操作） </summary>
        [BsonElement("transactionId")]
        public string? TransactionId { get; set; }

        #endregion

        #region Data Properties

        /// <summary> 
        /// 簡化的資料內容
        /// 使用扁平化結構，避免複雜的嵌套序列化
        /// </summary>
        [BsonElement("data")]
        public LogData? Data { get; set; }

        #endregion

        #region Context Properties

        /// <summary> 使用者識別碼 </summary>
        [BsonElement("userId")]
        public string? UserId { get; set; }

        /// <summary> 客戶端 IP 地址 </summary>
        [BsonElement("ipAddress")]
        public string? IpAddress { get; set; }

        /// <summary> 請求 URL </summary>
        [BsonElement("requestUrl")]
        public string? RequestUrl { get; set; }

        /// <summary> 瀏覽器資訊 </summary>
        [BsonElement("userAgent")]
        public string? UserAgent { get; set; }

        #endregion

        #region Error Properties

        /// <summary> 錯誤訊息 </summary>
        [BsonElement("errorMessage")]
        public string? ErrorMessage { get; set; }

        /// <summary> 堆疊追蹤 </summary>
        [BsonElement("stackTrace")]
        public string? StackTrace { get; set; }

        #endregion

        #region Entity Change Properties

        /// <summary> 實體類型（用於資料變更日誌） </summary>
        [BsonElement("entityType")]
        public string? EntityType { get; set; }

        /// <summary> 實體識別碼 </summary>
        [BsonElement("entityId")]
        public string? EntityId { get; set; }

        /// <summary> 操作類型（CREATE, UPDATE, DELETE） </summary>
        [BsonElement("operation")]
        public string? Operation { get; set; }

        #endregion

        #region Constructor

        /// <summary>
        /// 預設建構函式
        /// </summary>
        public SimpleLogEntry()
        {
        }

        /// <summary>
        /// 建構函式 - 基本日誌
        /// </summary>
        /// <param name="level">日誌級別</param>
        /// <param name="message">日誌訊息</param>
        /// <param name="source">來源</param>
        public SimpleLogEntry(string level, string message, string source = "System")
        {
            Level = level;
            Message = message;
            Source = source;
        }

        /// <summary>
        /// 建構函式 - 實體變更日誌
        /// </summary>
        /// <param name="operation">操作類型</param>
        /// <param name="entityType">實體類型</param>
        /// <param name="entityId">實體識別碼</param>
        /// <param name="message">日誌訊息</param>
        public SimpleLogEntry(string operation, string entityType, string entityId, string message)
        {
            Level = "Information";
            Operation = operation;
            EntityType = entityType;
            EntityId = entityId;
            Message = message;
            Source = "EntityFramework";
        }

        #endregion
    }

    /// <summary>
    /// 增強可讀性的日誌資料結構
    /// 提供清晰、結構化的變更資訊展示
    /// </summary>
    public class LogData
    {
        /// <summary> 操作類型 </summary>
        [BsonElement("operation")]
        public string? Operation { get; set; }

        /// <summary> 變更前的資料（結構化格式） </summary>
        [BsonElement("beforeData")]
        public Dictionary<string, object>? BeforeData { get; set; }

        /// <summary> 變更後的資料（結構化格式） </summary>
        [BsonElement("afterData")]
        public Dictionary<string, object>? AfterData { get; set; }

        /// <summary> 詳細的欄位變更資訊 </summary>
        [BsonElement("fieldChanges")]
        public Dictionary<string, FieldChangeInfo>? FieldChanges { get; set; }

        /// <summary> 變更的欄位列表 </summary>
        [BsonElement("changedFields")]
        public List<string>? ChangedFields { get; set; }

        /// <summary> 人類可讀的變更摘要 </summary>
        [BsonElement("summary")]
        public string? Summary { get; set; }

        /// <summary> 詳細的變更描述 </summary>
        [BsonElement("changeDescription")]
        public string? ChangeDescription { get; set; }

        /// <summary> 額外的中繼資料 </summary>
        [BsonElement("metadata")]
        public Dictionary<string, string>? Metadata { get; set; }

        /// <summary> 處理狀態 </summary>
        [BsonElement("status")]
        public string Status { get; set; } = "Success";

        /// <summary> 處理時間（毫秒） </summary>
        [BsonElement("processingTimeMs")]
        public long? ProcessingTimeMs { get; set; }

        /// <summary> 影響的記錄數量 </summary>
        [BsonElement("affectedRecords")]
        public int AffectedRecords { get; set; } = 1;

        #region Constructor

        /// <summary>
        /// 預設建構函式
        /// </summary>
        public LogData()
        {
            ChangedFields = new List<string>();
            FieldChanges = new Dictionary<string, FieldChangeInfo>();
            Metadata = new Dictionary<string, string>();
        }

        /// <summary>
        /// 建構函式 - 實體變更
        /// </summary>
        /// <param name="operation">操作類型</param>
        /// <param name="beforeData">變更前資料</param>
        /// <param name="afterData">變更後資料</param>
        /// <param name="changedFields">變更欄位</param>
        public LogData(string operation, Dictionary<string, object>? beforeData, Dictionary<string, object>? afterData, List<string>? changedFields = null)
        {
            Operation = operation;
            BeforeData = beforeData;
            AfterData = afterData;
            ChangedFields = changedFields ?? new List<string>();
            FieldChanges = new Dictionary<string, FieldChangeInfo>();
            Metadata = new Dictionary<string, string>();
        }

        #endregion

        #region Helper Methods

        /// <summary> 添加中繼資料 </summary>
        /// <param name="key">鍵</param>
        /// <param name="value">值</param>
        public void AddMetadata(string key, string value)
        {
            Metadata ??= new Dictionary<string, string>();
            Metadata[key] = value;
        }

        /// <summary> 添加變更欄位 </summary>
        /// <param name="fieldName">欄位名稱</param>
        public void AddChangedField(string fieldName)
        {
            ChangedFields ??= new List<string>();
            if (!ChangedFields.Contains(fieldName))
            {
                ChangedFields.Add(fieldName);
            }
        }

        /// <summary> 設定處理時間 </summary>
        /// <param name="startTime">開始時間</param>
        public void SetProcessingTime(DateTime startTime)
        {
            ProcessingTimeMs = (long)(DateTime.UtcNow - startTime).TotalMilliseconds;
        }

        /// <summary> 添加欄位變更資訊 </summary>
        /// <param name="fieldName">欄位名稱</param>
        /// <param name="beforeValue">變更前值</param>
        /// <param name="afterValue">變更後值</param>
        /// <param name="fieldType">欄位類型</param>
        public void AddFieldChange(string fieldName, object? beforeValue, object? afterValue, string? fieldType = null)
        {
            FieldChanges ??= new Dictionary<string, FieldChangeInfo>();
            FieldChanges[fieldName] = new FieldChangeInfo
            {
                FieldName = fieldName,
                BeforeValue = beforeValue,
                AfterValue = afterValue,
                FieldType = fieldType,
                IsChanged = !Equals(beforeValue, afterValue)
            };
        }

        /// <summary> 生成詳細的變更描述 </summary>
        public void GenerateChangeDescription()
        {
            if (FieldChanges == null || !FieldChanges.Any())
            {
                ChangeDescription = "無欄位變更";
                return;
            }

            var descriptions = new List<string>();
            foreach (var change in FieldChanges.Values.Where(c => c.IsChanged))
            {
                var beforeStr = FormatValue(change.BeforeValue);
                var afterStr = FormatValue(change.AfterValue);
                descriptions.Add($"{change.FieldName}: {beforeStr} → {afterStr}");
            }

            ChangeDescription = string.Join(", ", descriptions);
        }

        /// <summary> 格式化值為可讀字串 </summary>
        private string FormatValue(object? value)
        {
            return value switch
            {
                null => "null",
                string str => $"\"{str}\"",
                DateTime dt => dt.ToString("yyyy-MM-dd HH:mm:ss"),
                DateTimeOffset dto => dto.ToString("yyyy-MM-dd HH:mm:ss zzz"),
                bool b => b ? "是" : "否",
                _ => value.ToString() ?? "null"
            };
        }

        #endregion
    }

    /// <summary>
    /// 欄位變更資訊
    /// 提供詳細的欄位級別變更追蹤
    /// </summary>
    public class FieldChangeInfo
    {
        /// <summary> 欄位名稱 </summary>
        [BsonElement("fieldName")]
        public string FieldName { get; set; } = string.Empty;

        /// <summary> 變更前的值 </summary>
        [BsonElement("beforeValue")]
        public object? BeforeValue { get; set; }

        /// <summary> 變更後的值 </summary>
        [BsonElement("afterValue")]
        public object? AfterValue { get; set; }

        /// <summary> 欄位類型 </summary>
        [BsonElement("fieldType")]
        public string? FieldType { get; set; }

        /// <summary> 是否有變更 </summary>
        [BsonElement("isChanged")]
        public bool IsChanged { get; set; }

        /// <summary> 變更類型描述 </summary>
        [BsonElement("changeType")]
        public string? ChangeType { get; set; }

        /// <summary> 人類可讀的變更描述 </summary>
        [BsonElement("description")]
        public string? Description { get; set; }
    }

    /// <summary>
    /// 日誌級別列舉
    /// </summary>
    public static class LogLevels
    {
        public const string Debug = "Debug";
        public const string Information = "Information";
        public const string Warning = "Warning";
        public const string Error = "Error";
        public const string Critical = "Critical";
    }

    /// <summary>
    /// 操作類型列舉
    /// </summary>
    public static class OperationTypes
    {
        public const string Create = "CREATE";
        public const string Update = "UPDATE";
        public const string Delete = "DELETE";
        public const string Read = "READ";
        public const string Batch = "BATCH";
        public const string System = "SYSTEM";
        public const string Error = "ERROR";
    }
}
