using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text.Json;
using FAST_ERP_Backend.Interfaces.Common;
using FAST_ERP_Backend.Models.Common.Logging;
using Microsoft.Extensions.Logging;

namespace FAST_ERP_Backend.Services.Common
{
    /// <summary>
    /// 日誌資料處理器實現
    /// 將複雜的實體變更轉換為簡化、可讀的日誌格式
    /// </summary>
    public class LogDataProcessor : ILogDataProcessor
    {
        #region Private Fields

        private readonly ILogger<LogDataProcessor> _logger;
        private readonly LogProcessingOptions _options;
        private readonly JsonSerializerOptions _jsonOptions;

        // 安全類型快取
        private static readonly HashSet<Type> _safeTypes = new HashSet<Type>
        {
            typeof(string), typeof(int), typeof(long), typeof(decimal), typeof(bool),
            typeof(DateTime), typeof(DateTimeOffset), typeof(Guid), typeof(byte[]),
            typeof(float), typeof(double), typeof(short), typeof(byte), typeof(char),
            // 可空類型
            typeof(int?), typeof(long?), typeof(decimal?), typeof(bool?),
            typeof(DateTime?), typeof(DateTimeOffset?), typeof(Guid?),
            typeof(float?), typeof(double?), typeof(short?), typeof(byte?), typeof(char?)
        };

        #endregion

        #region Constructor

        /// <summary>
        /// 初始化日誌資料處理器
        /// </summary>
        /// <param name="logger">日誌服務</param>
        /// <param name="options">處理選項</param>
        public LogDataProcessor(ILogger<LogDataProcessor> logger, LogProcessingOptions? options = null)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _options = options ?? new LogProcessingOptions();

            // 配置 JSON 序列化選項
            _jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = false,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull,
                MaxDepth = _options.MaxSerializationDepth
            };
        }

        #endregion

        #region Public Methods

        /// <summary> 處理實體變更記錄 </summary>
        public SimpleLogEntry ProcessEntityChange(EntityChangeRecord record)
        {
            try
            {
                var startTime = DateTime.UtcNow;
                var operation = record.Operation.ToString().ToUpper();

                var logEntry = new SimpleLogEntry(operation, record.EntityType, record.EntityId,
                    GenerateEnhancedSummary(operation, record.EntityType, record.ChangedProperties, record.EntityId))
                {
                    Level = LogLevels.Information,
                    Source = "EntityFramework",
                    UserId = record.UserId
                };

                // 處理資料內容 - 使用結構化資料
                var beforeData = ConvertToStructuredData(record.BeforeValues);
                var afterData = ConvertToStructuredData(record.AfterValues);
                var logData = new LogData(operation, beforeData, afterData, record.ChangedProperties);

                switch (record.Operation)
                {
                    case EntityChangeOperation.Create:
                        logData.Summary = GenerateCreateSummary(record.EntityType, afterData);
                        logData.AffectedRecords = 1;
                        break;

                    case EntityChangeOperation.Update:
                        logData.Summary = GenerateUpdateSummary(record.EntityType, record.ChangedProperties);
                        ProcessFieldChanges(logData, beforeData, afterData, record.ChangedProperties);
                        logData.AffectedRecords = 1;
                        break;

                    case EntityChangeOperation.Delete:
                        logData.Summary = GenerateDeleteSummary(record.EntityType, beforeData);
                        logData.AffectedRecords = 1;
                        break;
                }

                // 生成詳細的變更描述
                logData.GenerateChangeDescription();
                logData.SetProcessingTime(startTime);
                logEntry.Data = logData;

                return logEntry;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "處理實體變更記錄時發生錯誤: {EntityType}", record.EntityType);
                return CreateErrorLogEntry($"處理實體變更失敗: {ex.Message}", record.EntityType);
            }
        }

        /// <summary> 處理簡單日誌 </summary>
        public SimpleLogEntry ProcessSimpleLog(string level, string message, string source, Exception? exception = null)
        {
            var logEntry = new SimpleLogEntry(level, message, source);

            if (exception != null)
            {
                logEntry.ErrorMessage = exception.Message;
                logEntry.StackTrace = exception.StackTrace;

                // 創建錯誤資料
                logEntry.Data = new LogData
                {
                    Operation = OperationTypes.Error,
                    Summary = $"系統錯誤: {exception.GetType().Name}",
                    Status = "Error"
                };

                logEntry.Data.AddMetadata("exceptionType", exception.GetType().Name);
                if (exception.InnerException != null)
                {
                    logEntry.Data.AddMetadata("innerException", exception.InnerException.Message);
                }
            }

            return logEntry;
        }

        /// <summary> 安全序列化物件 </summary>
        public string SafeSerialize(object? data)
        {
            if (data == null) return "null";

            try
            {
                // 如果是字典，直接序列化
                if (data is Dictionary<string, object> dict)
                {
                    var safeDict = FilterSensitiveData(dict);
                    return JsonSerializer.Serialize(safeDict, _jsonOptions);
                }

                // 提取安全屬性後序列化
                var safeProperties = ExtractSafeProperties(data);
                var filteredProperties = FilterSensitiveData(safeProperties);
                return JsonSerializer.Serialize(filteredProperties, _jsonOptions);
            }
            catch (JsonException ex) when (ex.Message.Contains("cycle") || ex.Message.Contains("depth"))
            {
                // 處理循環引用或深度問題
                _logger.LogWarning("檢測到循環引用或深度過深，使用簡化序列化");
                return SerializeWithoutCycles(data);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "序列化物件時發生錯誤");
                return $"{{\"serializationError\": \"{ex.Message.Replace("\"", "\\\"")}\", \"objectType\": \"{data.GetType().Name}\"}}";
            }
        }

        /// <summary> 安全序列化為結構化資料 </summary>
        public Dictionary<string, object>? SafeSerializeToStructured(object? data)
        {
            if (data == null) return null;

            try
            {
                // 如果是字典，直接處理
                if (data is Dictionary<string, object> dict)
                {
                    return FilterSensitiveDataStructured(dict);
                }

                // 提取安全屬性
                var safeProperties = ExtractSafeProperties(data);
                return FilterSensitiveDataStructured(safeProperties);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "結構化序列化物件時發生錯誤");
                return new Dictionary<string, object>
                {
                    ["serializationError"] = ex.Message,
                    ["objectType"] = data.GetType().Name
                };
            }
        }

        /// <summary> 無循環引用的序列化 </summary>
        private string SerializeWithoutCycles(object? data)
        {
            if (data == null) return "null";

            try
            {
                // 使用更安全的序列化選項
                var safeOptions = new JsonSerializerOptions
                {
                    WriteIndented = false,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull,
                    MaxDepth = 2, // 限制更淺的深度
                    ReferenceHandler = System.Text.Json.Serialization.ReferenceHandler.IgnoreCycles
                };

                // 如果是字典，直接序列化
                if (data is Dictionary<string, object> dict)
                {
                    var flatDict = FlattenDictionary(dict);
                    return JsonSerializer.Serialize(flatDict, safeOptions);
                }

                // 提取基本屬性
                var basicProperties = ExtractBasicProperties(data);
                return JsonSerializer.Serialize(basicProperties, safeOptions);
            }
            catch (Exception ex)
            {
                // 最後的備援方案
                return $"{{\"error\": \"無法序列化\", \"type\": \"{data.GetType().Name}\", \"message\": \"{ex.Message.Replace("\"", "\\\"")}\"}}";
            }
        }

        /// <summary> 扁平化字典，移除複雜物件 </summary>
        private Dictionary<string, object> FlattenDictionary(Dictionary<string, object> dict)
        {
            var result = new Dictionary<string, object>();

            foreach (var kvp in dict)
            {
                if (kvp.Value == null)
                {
                    result[kvp.Key] = "null";
                }
                else if (IsSimpleValue(kvp.Value))
                {
                    result[kvp.Key] = kvp.Value;
                }
                else
                {
                    result[kvp.Key] = $"[{kvp.Value.GetType().Name}]";
                }
            }

            return result;
        }

        /// <summary> 提取基本屬性（避免導航屬性） </summary>
        private Dictionary<string, object> ExtractBasicProperties(object entity)
        {
            var result = new Dictionary<string, object>();

            if (entity == null) return result;

            try
            {
                var entityType = entity.GetType();
                var properties = entityType.GetProperties(BindingFlags.Public | BindingFlags.Instance);

                foreach (var property in properties)
                {
                    try
                    {
                        // 跳過索引器和複雜屬性
                        if (property.GetIndexParameters().Length > 0 ||
                            IsNavigationProperty(property) ||
                            !IsSafeType(property.PropertyType))
                        {
                            continue;
                        }

                        var value = property.GetValue(entity);
                        if (value != null && IsSimpleValue(value))
                        {
                            result[property.Name] = ConvertToSafeValue(value);
                        }
                    }
                    catch (Exception ex)
                    {
                        result[property.Name] = $"[讀取錯誤: {ex.Message}]";
                    }
                }
            }
            catch (Exception ex)
            {
                result["extractionError"] = ex.Message;
            }

            return result;
        }

        /// <summary> 檢查是否為簡單值 </summary>
        private bool IsSimpleValue(object value)
        {
            if (value == null) return true;

            var type = value.GetType();
            return type.IsPrimitive ||
                   type.IsEnum ||
                   type == typeof(string) ||
                   type == typeof(decimal) ||
                   type == typeof(DateTime) ||
                   type == typeof(DateTimeOffset) ||
                   type == typeof(Guid) ||
                   type == typeof(TimeSpan);
        }

        /// <summary> 處理批次變更 </summary>
        public List<SimpleLogEntry> ProcessBatchChanges(EntityChangeSnapshot snapshot, string transactionId)
        {
            var logEntries = new List<SimpleLogEntry>();

            try
            {
                // 處理個別變更
                foreach (var change in snapshot.Changes)
                {
                    var logEntry = ProcessEntityChange(change);
                    logEntry.TransactionId = transactionId;
                    logEntries.Add(logEntry);
                }

                // 如果有多個變更，創建批次摘要
                if (snapshot.Changes.Count > 1)
                {
                    var batchSummary = CreateBatchSummary(snapshot, transactionId);
                    logEntries.Add(batchSummary);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "處理批次變更時發生錯誤");
                var errorEntry = CreateErrorLogEntry($"批次處理失敗: {ex.Message}", "Batch");
                errorEntry.TransactionId = transactionId;
                logEntries.Add(errorEntry);
            }

            return logEntries;
        }

        /// <summary> 提取安全屬性 </summary>
        public Dictionary<string, object> ExtractSafeProperties(object entity)
        {
            var result = new Dictionary<string, object>();

            if (entity == null) return result;

            try
            {
                var entityType = entity.GetType();
                var properties = entityType.GetProperties(BindingFlags.Public | BindingFlags.Instance);

                foreach (var property in properties)
                {
                    try
                    {
                        // 跳過索引器和導航屬性
                        if (property.GetIndexParameters().Length > 0 || IsNavigationProperty(property))
                        {
                            continue;
                        }

                        // 只處理安全類型
                        if (!IsSafeType(property.PropertyType))
                        {
                            continue;
                        }

                        var value = property.GetValue(entity);
                        if (value != null)
                        {
                            result[property.Name] = ConvertToSafeValue(value);
                        }
                    }
                    catch (Exception ex)
                    {
                        result[property.Name] = $"[讀取錯誤: {ex.Message}]";
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "提取安全屬性時發生錯誤");
                result["extractionError"] = ex.Message;
            }

            return result;
        }

        /// <summary> 計算欄位差異 </summary>
        public List<string> CalculateChangedFields(Dictionary<string, object>? beforeData, Dictionary<string, object>? afterData)
        {
            var changedFields = new List<string>();

            if (afterData == null) return changedFields;

            foreach (var kvp in afterData)
            {
                var beforeValue = beforeData?.GetValueOrDefault(kvp.Key);
                if (!Equals(beforeValue, kvp.Value))
                {
                    changedFields.Add(kvp.Key);
                }
            }

            return changedFields;
        }

        /// <summary> 生成變更摘要 </summary>
        public string GenerateChangeSummary(string operation, string entityType, List<string> changedFields)
        {
            return operation switch
            {
                "CREATE" => $"新增 {entityType} 實體",
                "UPDATE" => $"修改 {entityType} 實體，變更欄位: {string.Join(", ", changedFields)}",
                "DELETE" => $"刪除 {entityType} 實體",
                _ => $"處理 {entityType} 實體 ({operation})"
            };
        }

        /// <summary> 生成增強的摘要訊息 </summary>
        private string GenerateEnhancedSummary(string operation, string entityType, List<string> changedFields, string entityId)
        {
            var entityDisplayName = GetEntityDisplayName(entityType);
            var shortId = entityId.Length > 8 ? entityId.Substring(0, 8) + "..." : entityId;

            return operation switch
            {
                "CREATE" => $"✅ 新增{entityDisplayName} (ID: {shortId})",
                "UPDATE" => $"📝 修改{entityDisplayName} (ID: {shortId}) - 變更 {changedFields.Count} 個欄位: {string.Join(", ", changedFields.Take(3))}{(changedFields.Count > 3 ? "..." : "")}",
                "DELETE" => $"🗑️ 刪除{entityDisplayName} (ID: {shortId})",
                _ => $"🔄 處理{entityDisplayName} ({operation})"
            };
        }

        /// <summary> 獲取實體顯示名稱 </summary>
        private string GetEntityDisplayName(string entityType)
        {
            return entityType switch
            {
                // Common 模組
                "Users" => "使用者",
                "Department" => "部門",
                "Roles" => "角色",
                "RolesPermissions" => "角色權限",
                "AuditLogs" => "審計日誌",
                "SystemMenu" => "系統選單",
                "SystemGroups" => "系統群組",
                "EnterpriseGroups" => "企業群組",
                "Position" => "職位",
                "Division" => "部門",
                "Unit" => "單位",
                "City" => "城市",
                "District" => "區域",

                // IMS 模組 - 商業夥伴管理
                "Partner" => "商業夥伴",
                "IndividualDetail" => "自然人詳細資料",
                "EnterpriseDetail" => "法人詳細資料",
                "CustomerDetail" => "客戶詳細資料",
                "SupplierDetail" => "供應商詳細資料",
                "PartnerContact" => "夥伴聯絡人",
                "Contact" => "聯絡人",
                "ContactRole" => "聯絡人角色",
                "PartnerAddress" => "夥伴地址",
                "CustomerCategory" => "客戶類別",
                "SupplierCategory" => "供應商類別",
                "Item" => "品項",
                "ItemCategory" => "品項類別",
                "ItemUnit" => "品項單位",
                "ItemPrice" => "品項價格",
                "ItemStock" => "品項庫存",
                "Warehouse" => "倉庫",
                "WarehouseLocation" => "倉庫位置",

                // PMS 模組 - 資產管理
                "Asset" => "資產",
                "InsuranceUnit" => "保險單位",
                "Manufacturer" => "製造商",
                "StorageLocation" => "存放位置",
                "DepreciationForm" => "折舊表單",
                "DepreciationFormDetail" => "折舊表單明細",
                "AssetAccount" => "資產科目",
                "AssetSubAccount" => "資產子科目",
                "AssetSource" => "資產來源",
                "AssetCategory" => "資產類別",
                "AccessoryEquipment" => "附屬設備",
                "PmsSystemParameter" => "PMS系統參數",
                "AmortizationSource" => "攤銷來源",
                "AssetStatus" => "資產狀態",
                "PmsUserRole" => "PMS使用者角色",
                "PmsUserRoleMapping" => "PMS使用者角色對應",
                "EquipmentType" => "設備類型",
                "AssetCarryOut" => "資產攜出",
                "VendorMaintenance" => "廠商維護",
                "AssetLocationTransfer" => "資產位置轉移",
                "AssetLocationTransferDetail" => "資產位置轉移明細",

                // PAS 模組 - 人事管理
                "Employee" => "員工",
                "Education" => "教育背景",
                "Train" => "訓練記錄",
                "Examination" => "考試記錄",
                "Certification" => "證照",
                "Undergo" => "受訓記錄",
                "Ensure" => "保證記錄",
                "Suspend" => "停職記錄",
                "Salary" => "薪資",
                "Hensure" => "健保記錄",
                "Dependent" => "眷屬",
                "PerformancePointGroup" => "績效點數群組",
                "PerformancePointType" => "績效點數類型",
                "PerformancePointRecord" => "績效點數記錄",
                "RegularSalaryItem" => "固定薪資項目",
                "EmployeeRegularSalary" => "員工固定薪資",
                "SalaryPoint" => "薪資點數",
                "InsuranceGrade" => "保險等級",
                "InsuranceHistory" => "保險歷史",
                "Promotion" => "升遷記錄",
                "ExpenseDepartmentChange" => "費用部門異動",
                "ServiceDepartmentChange" => "服務部門異動",

                _ => entityType
            };
        }

        /// <summary> 轉換為結構化資料 </summary>
        private Dictionary<string, object>? ConvertToStructuredData(Dictionary<string, object>? data)
        {
            if (data == null) return null;

            var result = new Dictionary<string, object>();
            foreach (var kvp in data)
            {
                if (kvp.Value != null && IsSimpleValue(kvp.Value))
                {
                    result[kvp.Key] = ConvertToSafeValue(kvp.Value);
                }
            }
            return result;
        }

        /// <summary> 生成創建操作摘要 </summary>
        private string GenerateCreateSummary(string entityType, Dictionary<string, object>? afterData)
        {
            var entityDisplayName = GetEntityDisplayName(entityType);
            var fieldCount = afterData?.Count ?? 0;
            return $"成功新增{entityDisplayName}，包含 {fieldCount} 個欄位的資料";
        }

        /// <summary> 生成更新操作摘要 </summary>
        private string GenerateUpdateSummary(string entityType, List<string> changedFields)
        {
            var entityDisplayName = GetEntityDisplayName(entityType);
            var fieldList = string.Join("、", changedFields.Take(5));
            var moreFields = changedFields.Count > 5 ? $" 等 {changedFields.Count} 個欄位" : "";
            return $"成功修改{entityDisplayName}的 {fieldList}{moreFields}";
        }

        /// <summary> 生成刪除操作摘要 </summary>
        private string GenerateDeleteSummary(string entityType, Dictionary<string, object>? beforeData)
        {
            var entityDisplayName = GetEntityDisplayName(entityType);
            var fieldCount = beforeData?.Count ?? 0;
            return $"成功刪除{entityDisplayName}，保留 {fieldCount} 個欄位的歷史資料";
        }

        /// <summary> 處理欄位變更資訊 </summary>
        private void ProcessFieldChanges(LogData logData, Dictionary<string, object>? beforeData, Dictionary<string, object>? afterData, List<string> changedFields)
        {
            if (beforeData == null || afterData == null) return;

            foreach (var fieldName in changedFields)
            {
                var beforeValue = beforeData.GetValueOrDefault(fieldName);
                var afterValue = afterData.GetValueOrDefault(fieldName);
                var fieldType = afterValue?.GetType().Name ?? beforeValue?.GetType().Name;

                logData.AddFieldChange(fieldName, beforeValue, afterValue, fieldType);
            }
        }

        #endregion

        #region Private Methods

        /// <summary> 過濾敏感資料 </summary>
        private Dictionary<string, object> FilterSensitiveData(Dictionary<string, object> data)
        {
            if (!_options.IncludeSensitiveData)
            {
                var filtered = new Dictionary<string, object>();
                foreach (var kvp in data)
                {
                    if (_options.SensitiveFields.Any(field =>
                        kvp.Key.Contains(field, StringComparison.OrdinalIgnoreCase)))
                    {
                        filtered[kvp.Key] = "[敏感資料已隱藏]";
                    }
                    else
                    {
                        filtered[kvp.Key] = kvp.Value;
                    }
                }
                return filtered;
            }

            return data;
        }

        /// <summary> 過濾敏感資料（結構化版本） </summary>
        private Dictionary<string, object> FilterSensitiveDataStructured(Dictionary<string, object> data)
        {
            var filtered = new Dictionary<string, object>();

            foreach (var kvp in data)
            {
                if (!_options.IncludeSensitiveData &&
                    _options.SensitiveFields.Any(field =>
                        kvp.Key.Contains(field, StringComparison.OrdinalIgnoreCase)))
                {
                    filtered[kvp.Key] = "[敏感資料已隱藏]";
                }
                else if (kvp.Value != null && IsSimpleValue(kvp.Value))
                {
                    filtered[kvp.Key] = ConvertToSafeValue(kvp.Value);
                }
                else if (kvp.Value == null)
                {
                    filtered[kvp.Key] = null;
                }
                else
                {
                    filtered[kvp.Key] = $"[{kvp.Value.GetType().Name}]";
                }
            }

            return filtered;
        }

        /// <summary> 檢查是否為安全類型 </summary>
        private bool IsSafeType(Type type)
        {
            var underlyingType = Nullable.GetUnderlyingType(type) ?? type;
            return _safeTypes.Contains(type) || _safeTypes.Contains(underlyingType) || underlyingType.IsEnum;
        }

        /// <summary> 檢查是否為導航屬性 </summary>
        private bool IsNavigationProperty(PropertyInfo property)
        {
            // 檢查是否有 EF Core 導航屬性特徵
            return property.PropertyType.IsClass && 
                   property.PropertyType != typeof(string) && 
                   property.PropertyType != typeof(byte[]) &&
                   !IsSafeType(property.PropertyType);
        }

        /// <summary> 轉換為安全值 </summary>
        private object ConvertToSafeValue(object value)
        {
            return value switch
            {
                Guid guid => guid.ToString(),
                DateTime dateTime => dateTime.ToString("yyyy-MM-dd HH:mm:ss"),
                DateTimeOffset dateTimeOffset => dateTimeOffset.ToString("yyyy-MM-dd HH:mm:ss zzz"),
                _ => value
            };
        }

        /// <summary> 創建錯誤日誌記錄 </summary>
        private SimpleLogEntry CreateErrorLogEntry(string errorMessage, string entityType)
        {
            return new SimpleLogEntry(LogLevels.Error, errorMessage, "LogDataProcessor")
            {
                EntityType = entityType,
                Operation = OperationTypes.Error,
                ErrorMessage = errorMessage,
                Data = new LogData
                {
                    Operation = OperationTypes.Error,
                    Summary = errorMessage,
                    Status = "Error"
                }
            };
        }

        /// <summary> 創建批次摘要 </summary>
        private SimpleLogEntry CreateBatchSummary(EntityChangeSnapshot snapshot, string transactionId)
        {
            var operationCounts = snapshot.Changes
                .GroupBy(c => c.Operation.ToString().ToUpper())
                .ToDictionary(g => g.Key, g => g.Count());

            var entityCounts = snapshot.Changes
                .GroupBy(c => c.EntityType)
                .ToDictionary(g => GetEntityDisplayName(g.Key), g => g.Count());

            var summary = $"📦 批次操作完成 - 總計 {snapshot.TotalChanges} 個變更：" +
                         string.Join("、", operationCounts.Select(kv => $"{kv.Value}個{GetOperationDisplayName(kv.Key)}"));

            var detailedSummary = $"涉及實體：{string.Join("、", entityCounts.Select(kv => $"{kv.Key}({kv.Value}筆)"))}";

            var batchEntry = new SimpleLogEntry(OperationTypes.Batch, summary, "EntityFramework")
            {
                TransactionId = transactionId,
                Data = new LogData
                {
                    Operation = OperationTypes.Batch,
                    Summary = summary,
                    ChangeDescription = detailedSummary,
                    AffectedRecords = snapshot.TotalChanges
                }
            };

            // 添加批次統計資訊
            foreach (var kvp in operationCounts)
            {
                batchEntry.Data.AddMetadata($"{kvp.Key.ToLower()}Count", kvp.Value.ToString());
            }

            foreach (var kvp in entityCounts)
            {
                batchEntry.Data.AddMetadata($"entity_{kvp.Key}", kvp.Value.ToString());
            }

            batchEntry.Data.AddMetadata("totalChanges", snapshot.TotalChanges.ToString());
            batchEntry.Data.AddMetadata("captureTime", snapshot.CaptureTime.ToString("yyyy-MM-dd HH:mm:ss"));
            batchEntry.Data.AddMetadata("transactionDuration", "計算中...");

            return batchEntry;
        }

        /// <summary> 獲取操作顯示名稱 </summary>
        private string GetOperationDisplayName(string operation)
        {
            return operation switch
            {
                "CREATE" => "新增",
                "UPDATE" => "修改",
                "DELETE" => "刪除",
                _ => operation
            };
        }

        #endregion
    }
}
