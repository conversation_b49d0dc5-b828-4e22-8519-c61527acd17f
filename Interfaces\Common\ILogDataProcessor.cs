using System;
using System.Collections.Generic;
using FAST_ERP_Backend.Models.Common.Logging;

namespace FAST_ERP_Backend.Interfaces.Common
{
    /// <summary>
    /// 日誌資料處理器介面
    /// 負責將複雜的實體變更轉換為簡化的日誌格式
    /// </summary>
    public interface ILogDataProcessor
    {
        /// <summary> 處理實體變更記錄 </summary>
        /// <param name="record">實體變更記錄</param>
        /// <returns>簡化的日誌記錄</returns>
        SimpleLogEntry ProcessEntityChange(EntityChangeRecord record);

        /// <summary> 處理簡單日誌 </summary>
        /// <param name="level">日誌級別</param>
        /// <param name="message">日誌訊息</param>
        /// <param name="source">來源</param>
        /// <param name="exception">例外資訊</param>
        /// <returns>簡化的日誌記錄</returns>
        SimpleLogEntry ProcessSimpleLog(string level, string message, string source, Exception? exception = null);

        /// <summary> 安全序列化物件 </summary>
        /// <param name="data">要序列化的資料</param>
        /// <returns>序列化後的 JSON 字串</returns>
        string SafeSerialize(object? data);

        /// <summary> 安全序列化為結構化資料 </summary>
        /// <param name="data">要序列化的資料</param>
        /// <returns>結構化的資料字典</returns>
        Dictionary<string, object>? SafeSerializeToStructured(object? data);

        /// <summary> 處理批次變更 </summary>
        /// <param name="snapshot">實體變更快照</param>
        /// <param name="transactionId">交易識別碼</param>
        /// <returns>批次日誌記錄列表</returns>
        List<SimpleLogEntry> ProcessBatchChanges(EntityChangeSnapshot snapshot, string transactionId);

        /// <summary> 提取安全屬性 </summary>
        /// <param name="entity">實體物件</param>
        /// <returns>安全屬性字典</returns>
        Dictionary<string, object> ExtractSafeProperties(object entity);

        /// <summary> 計算欄位差異 </summary>
        /// <param name="beforeData">變更前資料</param>
        /// <param name="afterData">變更後資料</param>
        /// <returns>變更欄位列表</returns>
        List<string> CalculateChangedFields(Dictionary<string, object>? beforeData, Dictionary<string, object>? afterData);

        /// <summary> 生成變更摘要 </summary>
        /// <param name="operation">操作類型</param>
        /// <param name="entityType">實體類型</param>
        /// <param name="changedFields">變更欄位</param>
        /// <returns>變更摘要</returns>
        string GenerateChangeSummary(string operation, string entityType, List<string> changedFields);
    }

    /// <summary>
    /// 日誌處理選項
    /// </summary>
    public class LogProcessingOptions
    {
        /// <summary> 是否包含敏感資料 </summary>
        public bool IncludeSensitiveData { get; set; } = false;

        /// <summary> 最大序列化深度 </summary>
        public int MaxSerializationDepth { get; set; } = 3;

        /// <summary> 是否壓縮大型資料 </summary>
        public bool CompressLargeData { get; set; } = true;

        /// <summary> 大型資料閾值（字元數） </summary>
        public int LargeDataThreshold { get; set; } = 10000;

        /// <summary> 是否記錄詳細的變更資訊 </summary>
        public bool IncludeDetailedChanges { get; set; } = true;

        /// <summary> 敏感欄位列表 </summary>
        public HashSet<string> SensitiveFields { get; set; } = new HashSet<string>
        {
            "password", "token", "secret", "key", "credential"
        };
    }
}
